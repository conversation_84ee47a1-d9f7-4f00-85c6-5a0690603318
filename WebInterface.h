#ifndef WEBINTERFACE_H
#define WEBINTERFACE_H

#include <Arduino.h>
#include "Configuration.h"
#include "Display.h"
#include "ClockTime.h"
#include "NetworkService.h"

#if defined(ESP8266)
#include <ESP8266WebServer.h>
#else
#include <WebServer.h>
#endif

class WebInterface {
private:
#if defined(ESP8266)
  ESP8266WebServer* server;
#else
  WebServer* server;
#endif
  Configuration* config;
  Display* display;
  ClockTime* clockTime;
  NetworkService* network;
  
  bool handleAuth();
  void reqAuth();
  void handleRoot();
  void handleStyle();
  void handleOptions();
  void handleReset();
  void handleDownload();
  void handleLogout();
  void handleNotFound();
  
public:
#if defined(ESP8266)
  WebInterface(ESP8266WebServer* srv, Configuration* cfg, Display* disp, ClockTime* ct, NetworkService* net);
#else
  WebInterface(WebServer* srv, Configuration* cfg, Display* disp, ClockTime* ct, NetworkService* net);
#endif
  void start();
  void handleClient() { server->handleClient(); }
};

extern WebInterface webInterface;

#endif