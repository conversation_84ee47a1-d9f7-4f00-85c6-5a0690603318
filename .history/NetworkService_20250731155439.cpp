#include "NetworkService.h"
#include "Configuration.h"
#include "Display.h"

#if defined(ESP8266)
#include <ESP8266WiFi.h>
#include <ESP8266HTTPClient.h>
#else
#include <WiFi.h>
#include <HTTPClient.h>
#endif

#include <ArduinoJson.h>
#include <ArduinoOTA.h>

#ifdef SYSLOG
WiFiUDP udpClient;
Syslog syslog(udpClient, SYSLOG_PROTO_IETF);
#endif

static const char* UserAgent PROGMEM = "myClock/1.0 (Arduino ESP8266)";

void NetworkService::startWiFi() {
  WiFiManager wifiManager;
  extern char HOST[20];
  String hostname = String("myClock-") + String((uint32_t)ESP.getEfuseMac(), HEX);
  hostname.toCharArray(HOST, 20);
  
#if defined(ESP8266)
  WiFi.hostname(HOST);
#else
  WiFi.setHostname(HOST);
#endif

  wifiManager.setConfigPortalTimeout(300);
  wifiManager.setAPCallback([](WiFiManager *myWiFiManager) {
    OUT.println(F("startWiFi: entering config mode"));
    OUT.println(WiFi.softAPIP());
    OUT.println(myWiFiManager->getConfigPortalSSID());
  });

  if (!wifiManager.autoConnect(HOST, config.softAPpass.c_str())) {
    OUT.println(F("startWiFi: failed to connect and hit timeout"));
    delay(3000);
    ESP.restart();
    delay(5000);
  }

  OUT.println(F("startWiFi: WiFi connected"));
  OUT.print(F("startWiFi: IP address: "));
  OUT.println(WiFi.localIP());

  ArduinoOTA.setHostname(HOST);
  ArduinoOTA.setPassword(config.softAPpass.c_str());
  ArduinoOTA.begin();
}

String NetworkService::getIPlocation() {
  WiFiClient wifi;
  HTTPClient http;
  static const char URL[] PROGMEM = "http://ip-api.com/json";
  String payload;
  http.setUserAgent(UserAgent);
  if (!http.begin(wifi, URL)) {
#ifdef SYSLOG
    syslog.log(F("getIPlocation HTTP failed"));
#endif
    OUT.println(F("getIPlocation: HTTP failed"));
  } else {
    int stat = http.GET();
    if (stat == HTTP_CODE_OK) {
      payload = http.getString();
      JsonDocument jsonDoc;
      DeserializationError error = deserializeJson(jsonDoc, payload);
      if (!error) {
        JsonObject root = jsonDoc.as<JsonObject>();
        String isp = root["isp"];
        String region = root["regionName"];
        String country = root["countryCode"];
        String tz = root["timezone"];
        String zip = root["zip"];
        config.timezone = tz;
        config.countryCode = country;
        http.end();
#ifdef SYSLOG
        syslog.logf("getIPlocation: %s, %s, %s, %s",
                    isp.c_str(), region.c_str(), country.c_str(), tz.c_str());
#endif
        OUT.println(PSTR("getIPlocation: ") + isp + PSTR(", ")
                    + region + PSTR(", ") + country + PSTR(", ") + tz);
        return zip;
      } else {
#ifdef SYSLOG
        syslog.log(F("getIPlocation JSON parse failed"));
        syslog.log(payload);
#endif
        OUT.println(F("getIPlocation: JSON parse failed!"));
        OUT.println(payload);
      }
    } else {
#ifdef SYSLOG
      syslog.logf("getIPlocation failed, GET reply %d", stat);
#endif
      OUT.printf_P(PSTR("getIPlocation: GET reply %d\r\n"), stat);
    }
  }
  http.end();
  delay(1000);
  return "";
}

void NetworkService::getWeather() {
  wDelay = time(nullptr) + WDELAY;
  display.getMatrix()->setCursor(0, Display::row4);
  display.getMatrix()->setTextColor(Display::myRED);
  WiFiClient wifi;
  HTTPClient http;
  String URL = PSTR("http://api.openweathermap.org/data/2.5/weather?zip=")
               + config.location + F(",") + config.countryCode + F("&units=%units%&lang=%lang%&appid=") + config.owKey;
  URL.replace("%units%", config.celsius ? "metric" : "imperial");
  URL.replace("%lang%", config.language);
  String payload;
  http.setUserAgent(UserAgent);
  if (!http.begin(wifi, URL)) {
#ifdef SYSLOG
    syslog.log(F("getWeather HTTP failed"));
#endif
    display.getMatrix()->print(F("http fail"));
    OUT.println(F("getWeather: HTTP failed"));
  } else {
    int stat = http.GET();
    if (stat == HTTP_CODE_OK) {
      payload = http.getString();
      JsonDocument jsonDoc;
      DeserializationError error = deserializeJson(jsonDoc, payload);
      if (!error) {
        JsonObject root = jsonDoc.as<JsonObject>();
        String name = root["name"];
        JsonObject weather = root["weather"][0];
        JsonObject main = root["main"];
        float temperature = main["temp"];
        int humidity = main["humidity"];
        float wind = root["wind"]["speed"];
        int deg = root["wind"]["deg"];
        String dir = degreeDir(deg);
        int tc;
        if (config.celsius) tc = (int)round(temperature * 1.8) + 32;
        else tc = (int)round(temperature);
        
        if (tc <= 32) display.getMatrix()->setTextColor(Display::myCYAN);
        else if (tc <= 50) display.getMatrix()->setTextColor(Display::myLTBLUE);
        else if (tc <= 60) display.getMatrix()->setTextColor(Display::myBLUE);
        else if (tc <= 78) display.getMatrix()->setTextColor(Display::myGREEN);
        else if (tc <= 86) display.getMatrix()->setTextColor(Display::myYELLOW);
        else if (tc <= 95) display.getMatrix()->setTextColor(Display::myORANGE);
        else if (tc > 95) display.getMatrix()->setTextColor(Display::myRED);
        else display.getMatrix()->setTextColor(Display::myColor);
        
        display.getMatrix()->fillRect(0, 0, 64, 6, Display::myBLACK);
#ifdef DS18
        display.getMatrix()->setCursor(0, Display::row1);
        display.getMatrix()->printf_P(PSTR("%2d/%2d%c%s %2d%% %2d %s"), display.Temp, (int)round(temperature),
                         142, config.celsius ? "C" : "F", humidity, (int)round(wind), dir.c_str());
#else
        display.getMatrix()->setCursor(9, Display::row1);
        display.getMatrix()->printf_P(PSTR("% 2d%c%s %2d%% %2d %s"), (int)round(temperature),
                         142, config.celsius ? "C" : "F", humidity, (int)round(wind), dir.c_str());
#endif
        String description = weather["description"];
        description.replace(F("intensity "), "");
        description = utf8ascii(description);
        int id = weather["id"];
        int i = id / 100;
        switch (i) {
          case 2: display.getMatrix()->setTextColor(Display::myORANGE); break;
          case 3: display.getMatrix()->setTextColor(Display::myBLUE); break;
          case 5: display.getMatrix()->setTextColor(Display::myBLUE); break;
          case 6: display.getMatrix()->setTextColor(Display::myWHITE); break;
          case 7: display.getMatrix()->setTextColor(Display::myYELLOW); break;
          case 8: display.getMatrix()->setTextColor(Display::myGRAY); break;
        }
        int16_t  x1, y1, ww;
        uint16_t w, h;
        display.getMatrix()->getTextBounds(description, 0, Display::row4, &x1, &y1, &w, &h);
        display.getMatrix()->fillRect(0, 25, 64, 7, Display::myBLACK);
        if (w < 64) x1 = (68 - w) >> 1;
        display.getMatrix()->setCursor(x1, Display::row4);
        display.getMatrix()->print(description);
#ifdef SYSLOG
        syslog.logf("getWeather: %dF|%d%%RH|%d%s|%s",
                    (int)round(temperature), humidity, (int)round(wind), dir.c_str(), description.c_str());
#endif
        OUT.printf_P(PSTR("%2dF, %2d%%, %d %s (%d), %s (%d) \r\n"),
                     (int)round(temperature), humidity, (int)round(wind), dir.c_str(), deg, description.c_str(), id);
      } else {
        display.getMatrix()->print(F("json fail"));
#ifdef SYSLOG
        syslog.log(F("getWeather JSON parse failed"));
        syslog.log(payload);
#endif
        OUT.println(F("getWeather: JSON parse failed!"));
        OUT.println(payload);
      }
    } else {
#ifdef SYSLOG
      syslog.logf("getWeather failed, GET reply %d", stat);
#endif
      display.getMatrix()->print(stat);
      OUT.printf_P(PSTR("getWeather: GET failed: %d %s\r\n"), stat, http.errorToString(stat).c_str());
    }
  }
  http.end();
}

String NetworkService::degreeDir(int degrees) {
  static const char* caridnals[] PROGMEM = { "N", "NE", "E", "SE", "S", "SW", "W", "NW", "N" };
  return caridnals[(int)round((degrees % 360) / 45)];
}

static byte c1;

byte NetworkService::utf8ascii(byte ascii) {
  if ( ascii < 128 ) {
    c1 = 0;
    return ( ascii );
  }
  byte last = c1;
  c1 = ascii;
  switch (last) {
    case 0xC2: return  (ascii);  break;
    case 0xC3: return  (ascii | 0xC0) - 34;  break;
    case 0x82: if (ascii == 0xAC) return (0x80);
  }
  return  (0);
}

String NetworkService::utf8ascii(String s) {
  String r = "";
  char c;
  for (int i = 0; i < s.length(); i++) {
    c = utf8ascii(s.charAt(i));
    if (c != 0) r += c;
  }
  return r;
}

void NetworkService::utf8ascii(char* s) {
  int k = 0;
  char c;
  for (int i = 0; i < strlen(s); i++) {
    c = utf8ascii(s[i]);
    if (c != 0)
      s[k++] = c;
  }
  s[k] = 0;
}