#include "Configuration.h"
#include <ArduinoJson.h>
#include <FS.h>

#if defined(ESP8266)
#include <LittleFS.h>
#define FILESYSTEM LittleFS
#else
#include <SPIFFS.h>
#define FILESYSTEM SPIFFS
#endif

#if myOUT == 0
NullStream NullStream;
Stream & OUT = NullStream;
#elif myOUT == 2
#include "BluetoothSerial.h"
BluetoothSerial SerialBT;
Stream & OUT = SerialBT;
#else
Stream & OUT = Serial;
#endif

void Configuration::readSPIFFS() {
  if (FILESYSTEM.begin()) {
    OUT.println(F("readSPIFFS: mounted file system"));
    if (FILESYSTEM.exists("/config.json")) {
      OUT.println(F("readSPIFFS: reading config file"));
      File configFile = FILESYSTEM.open("/config.json", "r");
      if (configFile) {
        OUT.println(F("readSPIFFS: opened config file"));
        size_t size = configFile.size();
        std::unique_ptr<char[]> buf(new char[size]);
        configFile.readBytes(buf.get(), size);
        DynamicJsonBuffer jsonBuffer;
        JsonObject& json = jsonBuffer.parseObject(buf.get());
        configFile.close();
        if (json.success()) {
          OUT.println(F("readSPIFFS: parsed json"));
          if (json.containsKey("softAPpass")) softAPpass = (const char*)json["softAPpass"];
          if (json.containsKey("tzKey")) tzKey = (const char*)json["tzKey"];
          if (json.containsKey("owKey")) owKey = (const char*)json["owKey"];
          if (json.containsKey("brightness")) brightness = json["brightness"];
          if (json.containsKey("milTime")) milTime = json["milTime"];
          if (json.containsKey("location")) location = (const char*)json["location"];
          if (json.containsKey("timezone")) timezone = (const char*)json["timezone"];
          if (json.containsKey("threshold")) threshold = json["threshold"];
          if (json.containsKey("celsius")) celsius = json["celsius"];
          if (json.containsKey("language")) language = (const char*)json["language"];
          if (json.containsKey("countryCode")) countryCode = (const char*)json["countryCode"];
#ifdef SYSLOG
          if (json.containsKey("syslogSrv")) syslogSrv = (const char*)json["syslogSrv"];
          if (json.containsKey("syslogPort")) syslogPort = json["syslogPort"];
#endif
        } else {
          OUT.println(F("readSPIFFS: failed to load json config"));
        }
      }
    }
  } else {
    OUT.println(F("readSPIFFS: failed to mount FS"));
  }
}

void Configuration::writeSPIFFS() {
  OUT.println(F("writeSPIFFS: saving config"));
  DynamicJsonBuffer jsonBuffer;
  JsonObject& json = jsonBuffer.createObject();
  json["softAPpass"] = softAPpass;
  json["tzKey"] = tzKey;
  json["owKey"] = owKey;
  json["brightness"] = brightness;
  json["milTime"] = milTime;
  json["location"] = location;
  json["timezone"] = timezone;
  json["threshold"] = threshold;
  json["celsius"] = celsius;
  json["language"] = language;
  json["countryCode"] = countryCode;
#ifdef SYSLOG
  json["syslogSrv"] = syslogSrv;
  json["syslogPort"] = syslogPort;
#endif
  File configFile = FILESYSTEM.open("/config.json", "w");
  if (!configFile) {
    OUT.println(F("writeSPIFFS: failed to open config file for writing"));
  }
  json.printTo(configFile);
  configFile.close();
}

String Configuration::getSPIFFS() {
  DynamicJsonBuffer jsonBuffer;
  JsonObject& json = jsonBuffer.createObject();
  json["softAPpass"] = softAPpass;
  json["tzKey"] = tzKey;
  json["owKey"] = owKey;
  json["brightness"] = brightness;
  json["milTime"] = milTime;
  json["location"] = location;
  json["timezone"] = timezone;
  json["threshold"] = threshold;
  json["celsius"] = celsius;
  json["language"] = language;
  json["countryCode"] = countryCode;
#ifdef SYSLOG
  json["syslogSrv"] = syslogSrv;
  json["syslogPort"] = syslogPort;
#endif
  String output;
  json.printTo(output);
  return output;
}