#include "ClockTime.h"
#include "Configuration.h"
#include <ArduinoJson.h>

#if defined(ESP8266)
#include <ESP8266HTTPClient.h>
#else
#include <HTTPClient.h>
#endif

#ifdef SYSLOG
#include <Syslog.h>
extern WiFiUDP udpClient;
extern Syslog syslog;
#endif

static const char* UserAgent PROGMEM = "myClock/1.0 (Arduino ESP8266)";

int ClockTime::getOffset(const String tz) {
  WiFiClient wifi;
  HTTPClient http;
  String URL = PSTR("http://api.timezonedb.com/v2.1/get-time-zone?key=")
               + config.tzKey + PSTR("&format=json&by=zone&country=") + config.countryCode + PSTR("&zone=") + tz;
  String payload;
  int stat;
  long offset;
  
  http.setUserAgent(UserAgent);
  if (!http.begin(wifi, URL)) {
#ifdef SYSLOG
    syslog.log(F("getOffset HTTP failed"));
#endif
    OUT.println(F("getOffset: HTTP failed"));
  } else {
    stat = http.GET();
    if (stat == HTTP_CODE_OK) {
      payload = http.getString();
      JsonDocument jsonDoc;
      DeserializationError error = deserializeJson(jsonDoc, payload);
      if (!error) {
        JsonObject root = jsonDoc.as<JsonObject>();
        offset = root["gmtOffset"];
        OUT.println(PSTR("getOffset: ") + String(offset));
      } else {
#ifdef SYSLOG
        syslog.log(F("getOffset JSON parse failed"));
        syslog.log(payload);
#endif
        OUT.println(F("getOffset: JSON parse failed!"));
        OUT.println(payload);
      }
    } else {
#ifdef SYSLOG
      syslog.logf("getOffset failed, GET reply %d", stat);
#endif
      OUT.printf_P(PSTR("getOffset: GET reply %d\r\n"), stat);
    }
  }
  http.end();
  return stat;
}

void ClockTime::setNTP(const String tz) {
  while (getOffset(tz) != HTTP_CODE_OK) {
    delay(1000);
  }
  OUT.print(F("setNTP: configure NTP ..."));
  configTime(offset, 0, "pool.ntp.org");
  while (time(nullptr) < (30 * 365 * 24 * 60 * 60)) {
    delay(1000);
    OUT.print(F("."));
  }
  OUT.println(F(" OK"));
  time_t now = time(nullptr);
  struct tm * calendar;
  calendar = localtime(&now);
  calendar->tm_mday++;
  calendar->tm_hour = 2;
  calendar->tm_min = 0;
  calendar->tm_sec = 0;
  TWOAM = mktime(calendar);
  String t = ctime(&TWOAM);
  t.trim();
  OUT.print(F("setNTP: next timezone check @ "));
  OUT.println(t);
#ifdef SYSLOG
  syslog.logf("setNTP: %s (%d)", config.timezone.c_str(), int(offset / 3600));
#endif
}