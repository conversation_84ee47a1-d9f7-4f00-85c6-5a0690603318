#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/Display.cpp"
#include "Display.h"
#include "Configuration.h"
#include "TomThumb.h"
#include <WiFi.h>

#ifdef DS18
#include <OneWire.h>
#include <DallasTemperature.h>
#define ONE_WIRE_BUS D3
OneWire oneWire(ONE_WIRE_BUS);
DallasTemperature sensors(&oneWire);
#endif

MatrixPanel_I2S_DMA* dma_display = nullptr;

// Color definitions
const uint16_t Display::myRED = 0xF800;
const uint16_t Display::myGREEN = 0x07E0;
const uint16_t Display::myBLUE = 0x001F;
const uint16_t Display::myLTBLUE = 0x841F;
const uint16_t Display::myWHITE = 0xFFFF;
const uint16_t Display::myYELLOW = 0xFFE0;
const uint16_t Display::myORANGE = 0xFD20;
const uint16_t Display::myCYAN = 0x07FF;
const uint16_t Display::myMAGENTA = 0xF81F;
const uint16_t Display::myGRAY = 0x8410;
const uint16_t Display::myBLACK = 0x0000;

uint16_t Display::myColor = Display::myGREEN;

Display::Display() : 
  dma_display(nullptr),
  digit0(nullptr, 0, 63 - 1 - 9 * 1, 9, myColor),
  digit1(nullptr, 0, 63 - 1 - 9 * 2, 9, myColor),
  digit2(nullptr, 0, 63 - 4 - 9 * 3, 9, myColor),
  digit3(nullptr, 0, 63 - 4 - 9 * 4, 9, myColor),
  digit4(nullptr, 0, 63 - 7 - 9 * 5, 9, myColor),
  digit5(nullptr, 0, 63 - 7 - 9 * 6, 9, myColor)
{
#ifdef LIGHT
  light = 500; // threshold default
#endif
#ifdef DS18
  Temp = 0;
#endif
}

void Display::begin() {
  HUB75_I2S_CFG::i2s_pins pins = {
    R1_PIN, G1_PIN, B1_PIN, R2_PIN, G2_PIN, B2_PIN,
    A_PIN, B_PIN, C_PIN, D_PIN, E_PIN,
    LAT_PIN, OE_PIN, CLK_PIN
  };
  
  HUB75_I2S_CFG mxconfig(PANEL_RES_X, PANEL_RES_Y, PANEL_CHAIN, pins);
  mxconfig.i2sspeed = HUB75_I2S_CFG::HZ_10M;

  dma_display = new MatrixPanel_I2S_DMA(mxconfig);
  dma_display->begin();
  dma_display->setBrightness8(DEFAULT_BRIGHTNESS);
  dma_display->clearScreen();
  
  // Update digit references
  digit0.updateDisplay(dma_display);
  digit1.updateDisplay(dma_display);
  digit2.updateDisplay(dma_display);
  digit3.updateDisplay(dma_display);
  digit4.updateDisplay(dma_display);
  digit5.updateDisplay(dma_display);
  
  dma_display->setFont(&TomThumb);
  dma_display->setTextWrap(false);
  dma_display->setTextColor(myColor);
}

void Display::displayDraw(uint8_t b) {
  dma_display->clearScreen();
  dma_display->setBrightness8(b);
  dim = b;
  time_t now = time(nullptr);
  int ss = now % 60;
  int mm = (now / 60) % 60;
  int hh = (now / (60 * 60)) % 24;
  if ((!config.milTime) && (hh > 12)) hh -= 12;
  OUT.printf_P(PSTR("%02d:%02d\r"), hh, mm);
  digit1.DrawColon(myColor);
  digit3.DrawColon(myColor);
  digit0.Draw(ss % 10, myColor);
  digit1.Draw(ss / 10, myColor);
  digit2.Draw(mm % 10, myColor);
  digit3.Draw(mm / 10, myColor);
  digit4.Draw(hh % 10, myColor);
  digit5.Draw(hh / 10, myColor);
}

void Display::showSplash() {
  dma_display->clearScreen();
  dma_display->setCursor(2, row2);
  dma_display->setTextColor(myGREEN);
  dma_display->print(F("myClockOrg"));
  dma_display->setCursor(2, row3);
  dma_display->setTextColor(myBLUE);
  dma_display->print(F("Starting..."));
}

void Display::showBootInfo(const char* hostname, IPAddress ip, const String& tz) {
  dma_display->clearScreen();
  dma_display->setCursor(2, row1);
  dma_display->setTextColor(myGREEN);
  dma_display->print(hostname);
  dma_display->setCursor(2, row2);
  dma_display->setTextColor(myBLUE);
  dma_display->print(ip);
  dma_display->setCursor(2, row3);
  dma_display->setTextColor(myMAGENTA);
  dma_display->print(tz);
  dma_display->setCursor(2, row4);
  dma_display->setTextColor(myLTBLUE);
  dma_display->print(F("V1.0.0"));
}

#ifdef LIGHT
void Display::getLight() {
  int lt = analogRead(A0);
  if (lt > 20) {
    light = (light * 3 + lt) >> 2;
    if (light >= config.threshold) dim = config.brightness;
    else if (light < (config.threshold >> 3)) dim = config.brightness >> 4;
    else if (light < (config.threshold >> 2)) dim = config.brightness >> 3;
    else if (light < (config.threshold >> 1)) dim = config.brightness >> 2;
    else if (light < config.threshold) dim = config.brightness >> 1;
    dma_display->setBrightness8(dim);
  }
}
#endif

#ifdef DS18
void Display::updateTemperature() {
  sensors.requestTemperatures();
  int t;
  if (config.celsius) t = (int)round(sensors.getTempC(0));
  else t = (int)round(sensors.getTempF(0));
  if (t < -66 | t > 150) t = 0;
  if (Temp != t) {
    Temp = t;
    dma_display->setCursor(0, row1);
    dma_display->printf_P(PSTR("% 2d"), Temp);
  }
}
#endif

uint16_t Display::htmlColor565(const String htmlColor) {
  long c = strtol(htmlColor.substring(1).c_str(), NULL, 16);
  uint8_t r = (c >> 19);
  uint8_t g = (c >> 10) & 0x3F;
  uint8_t b = (c >> 3) & 0x1F;
  return ((r << 11) | (g << 5) | b);
}

uint32_t Display::color565to888(uint16_t c) {
  uint16_t r = (c >> 11) & 0x01f;
  uint16_t g = (c >> 5) & 0x03f;
  uint16_t b = (c) & 0x01f;
  r <<= 3;
  g <<= 2;
  b <<= 3;
  return ((r << 16) | (g << 8) | b);
}