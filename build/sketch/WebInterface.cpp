#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/WebInterface.cpp"
#include "WebInterface.h"
#include <ArduinoOTA.h>

#if defined(ESP8266)
#include <ESP8266mDNS.h>
#else
#include <ESPmDNS.h>
#include <Update.h>
#endif

static const char* serverHead PROGMEM =
  "<!DOCTYPE HTML><html><head>\n<title>myClockOrganized</title>\n"
  "<meta name='viewport' content='width=device-width, initial-scale=.83'>"
  "<link href='stylesheet.css' rel='stylesheet' type='text/css'></head>\n"
  "<body><a href='https://github.com/dragondaud/myClock' target='_blank'>\n"
  "<h1>myClockOrganized V1.0.0</h1></a>\n";

static const char* serverStyle PROGMEM =
  "body {background-color: Dark<PERSON>late<PERSON><PERSON>; color: White; font-family: sans-serif;}\n"
  "a {text-decoration: none; color: LightSteelBlue;}\n"
  "a:hover {text-decoration: underline; color: SteelBlue;}\n"
  "div {min-width: 420px; max-width: 600px; border: ridge; padding: 10px; background-color: SlateGray;}\n"
  "td {padding: 4px; text-align: left;}\n"
  "th {padding: 4px; text-align: right;}\n"
  "input[type=range] {vertical-align: middle;}\n"
  "input[type=file]::-webkit-file-upload-button {padding:10px 15px; -webkit-border-radius: 5px;}\n"
  ".button {padding:10px 15px; background:#ccc; -webkit-border-radius: 5px;}\n"
  "meter {width: 400px; vertical-align: middle;}\n"
  "meter::after {content: attr(value); position:relative; top:-17px; color: Black;}\n"
  "meter::-webkit-meter-bar {background: none; background-color: LightBlue; "
  "box-shadow: 5px 5px 5px SlateGray inset; border: 1px solid; }\n"
  ".switch {position: relative; display: inline-block; width: 60px; height: 34px;}\n"
  ".switch input {opacity: 0; width: 0; height: 0;}\n"
  ".slider {position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #ccc; -webkit-transition: .4s; transition: .4s;}\n"
  ".slider:before {position: absolute; content: ''; height: 26px; width: 26px; left: 4px; bottom: 4px; background-color: white; -webkit-transition: .4s; transition: .4s;}\n"
  "input:checked + .slider {background-color: DarkSlateGray;}\n"
  "input:focus + .slider {box-shadow: 0 0 1px SlateGray;}\n"
  "input:checked + .slider:before {-webkit-transform: translateX(26px); -ms-transform: translateX(26px); transform: translateX(26px);}\n"
  ".slider.round {border-radius: 34px;}\n"
  ".slider.round:before {border-radius: 50%;}\n";

static const char* serverOptions PROGMEM =
  "<div><h3>%host%</h3>\n"
  "<form method='POST' action='/options' id='optionsForm' name='optionsForm'>\n"
  "<table><tr><th><label for='myColor'>Color</label></th>\n"
  "<td><input type='color' id='myColor' name='myColor' value='%myColor%'></td></tr>\n"
  "<tr><th><label for='brightness'>Brightness</label></th>\n"
  "<td><input type='number' id='brightNum' name='brightNum' style='width: 3em'"
  "min='1' max='255' value='%brightness%' oninput='brightness.value=brightNum.value'> \n"
  "<input type='range' id='brightness' name='brightness' "
  "min='1' max='255' value='%brightness%' oninput='brightNum.value=brightness.value'></td></tr>\n"
  "<tr><th><label for='threshold'>Threshold</label></th>\n"
  "<td><input type='number' id='threshNum' name='threshNum' style='width: 3em'"
  "min='1' max='999' value='%threshold%' oninput='threshold.value=threshNum.value'> \n"
  "<input type='range' id='threshold' name='threshold' "
  "min='1' max='999' value='%threshold%' oninput='threshNum.value=threshold.value'></td></tr>\n"
  "<tr><th><label for='milTime'>24hour Time</label></th>\n"
  "<td><label class='switch'><input type='checkbox' id='milTime' name='milTime' %milTime%>"
  "<span class='slider round'></span></label></td></tr>\n"
  "<tr><th><label for='celsius'>Celsius</label></th>\n"
  "<td><label class='switch'><input type='checkbox' id='celsius' name='celsius' %celsius%>"
  "<span class='slider round'></span></label></td></tr>\n"
  "<tr><th><label for='language'>Language</label></th>\n"
  "<td><select name='language' id='language'>\n"
  "<option value='hr'>Croatian</option>\n"
  "<option value='cz'>Czech</option>\n"
  "<option value='nl'>Dutch</option>\n"
  "<option value='en'>English</option>\n"
  "<option value='fi'>Finnish</option>\n"
  "<option value='fr'>French</option>\n"
  "<option value='gl'>Galician</option>\n"
  "<option value='de'>German</option>\n"
  "<option value='hu'>Hungarian</option>\n"
  "<option value='it'>Italian</option>\n"
  "<option value='la'>Latvian</option>\n"
  "<option value='lt'>Lithuanian</option>\n"
  "<option value='pl'>Polish</option>\n"
  "<option value='pt'>Portuguese</option>\n"
  "<option value='sk'>Slovak</option>\n"
  "<option value='sl'>Slovenian</option>\n"
  "<option value='es'>Spanish</option></select></td></tr>\n"
  "<tr><th><label for='timezone'>Time Zone</label></th>\n"
  "<td><input type='text' id='timezone' name='timezone' value='%timezone%'></td></tr>\n"
  "<tr><th><label for='location'>Postal Code</label></th>\n"
  "<td><input type='text' id='location' name='location' size='10' value='%location%'></td></tr>\n"
  "<tr><th><label for='tzKey'>TimeZoneDB Key</label></th>\n"
  "<td><input type='text' id='tzKey' name='tzKey' value='%tzKey%'></td></tr>\n"
  "<tr><th><label for='owKey'>OpenWeatherMap Key</label></th>\n"
  "<td><input type='text' id='owKey' name='owKey' value='%owKey%'></td></tr>\n"
#ifdef SYSLOG
  "<tr><th><label for='syslogSrv'>SysLog Server</label></th>\n"
  "<td><input type='text' id='syslogSrv' name='owKey' value='%syslogSrv%'></td>  \n"
  "<th><label for='syslogPort'>Port</label></th>\n"
  "<td><input type='number' id='syslogPort' name='owKey' min='1' max='65535' value='%syslogPort%'></td></tr>\n"
#endif
  "<tr><th><label for='softAPpass'>Admin Password</label></th>\n"
  "<td><input type='password' id='softAPpass' name='softAPpass'\n"
  "minlength='8' placeholder='enter new password'></td></tr>\n"
  "<tr><td style='text-align: right'>Free Heap: %heap%</td>\n"
#ifdef LIGHT
  "<td>Light Level: %light%</td>\n"
#endif
  "<td><input type='submit' class='button' value='APPLY CONFIG' autofocus></td>\n"
  "</tr></table></form></div><p>\n";

static const char* serverUpdate PROGMEM =
  "<div><h3>Update Firmware</h3>\n"
  "<form method='POST' action='/update' enctype='multipart/form-data'>\n"
  "<input type='file' name='update'>\n"
  "<input type='submit' value='UPDATE' class='button'></form><p>\n"
  "<p><span><form method='GET' action='/reset' style='display:inline'>\n"
  "<input type='submit' value='REBOOT CLOCK' class='button'></form>\n"
  "<form method='GET' action='/download' style='display:inline'>\n"
  "<input type='submit' value='SAVE CONFIG' class='button'></form>\n"
  "<form method='GET' action='/logout' style='display:inline'>\n"
  "<input type='submit' value='LOGOUT' class='button'></form></span>\n"
  "<p></div></body></html>\n";

static const char* serverReboot PROGMEM =
  "<!DOCTYPE HTML><html><head>\n"
  "<meta http-equiv=\"refresh\" content=\"15;url=/\" />"
  "<style>body {background-color: DarkSlateGray; color: White;}"
  "</style></head>\n"
  "<body><h1>myClockOrganized V1.0.0</h1>"
  "Rebooting...</body></html>";

static const char* textPlain PROGMEM = "text/plain";
static const char* textHtml PROGMEM = "text/html";
static const char* textCss PROGMEM = "text/css";
static const char* checked PROGMEM = "checked";

extern char HOST[20];

#if defined(ESP8266)
WebInterface::WebInterface(ESP8266WebServer* srv, Configuration* cfg, Display* disp, ClockTime* ct, NetworkService* net) {
#else
WebInterface::WebInterface(WebServer* srv, Configuration* cfg, Display* disp, ClockTime* ct, NetworkService* net) {
#endif
  server = srv;
  config = cfg;
  display = disp;
  clockTime = ct;
  network = net;
}

bool WebInterface::handleAuth() {
  return server->authenticate(ADMIN_USER, config->softAPpass.c_str());
}

void WebInterface::reqAuth() {
  return server->requestAuthentication(DIGEST_AUTH, HOST);
}

void WebInterface::handleNotFound() {
#ifdef SYSLOG
  syslog.log(F("webServer: Not Found"));
#endif
  server->sendHeader(F("Location"), F("/"));
  server->send(301);
}

void WebInterface::handleStyle() {
  if (!handleAuth()) return reqAuth();
  server->send(200, textCss, String(serverStyle));
}

void WebInterface::handleOptions() {
  if (!handleAuth()) return reqAuth();
  if (!server->hasArg(F("myColor"))) return server->send(503, textPlain, F("FAILED"));
  String c = server->arg(F("myColor"));
  if (c != "") Display::myColor = Display::htmlColor565(c);
  uint8_t b = server->arg(F("brightness")).toInt();
  if (b) config->brightness = b;
  else config->brightness = 255;
  int t = server->arg(F("threshold")).toInt();
  if (t) config->threshold = t;
  else config->threshold = 500;
  c = server->arg(F("milTime"));
  if (c == "on") config->milTime = true;
  else config->milTime = false;
  c = server->arg(F("celsius"));
  if (c == "on") config->celsius = true;
  else config->celsius = false;
  c = server->arg(F("language"));
  if (c != "") config->language = c;
  c = server->arg(F("softAPpass"));
  if (c != "") config->softAPpass = c;
  c = server->arg(F("location"));
  if (c != "") config->location = c;
  c = server->arg(F("timezone"));
  if (c != "") {
    c.trim();
    c.replace(' ', '_');
    if (config->timezone != c) {
      config->timezone = c;
      clockTime->setNTP(config->timezone);
      delay(1000);
    }
  }
  c = server->arg(F("tzKey"));
  if (c != "") config->tzKey = c;
  c = server->arg(F("owKey"));
  if (c != "") config->owKey = c;
  display->displayDraw(config->brightness);
  network->getWeather();
  config->writeSPIFFS();
  server->sendHeader(F("Location"), F("/"));
  server->send(301);
}

void WebInterface::handleRoot() {
  if (!handleAuth()) return reqAuth();
  size_t fh = ESP.getFreeHeap();
#ifdef SYSLOG
  syslog.log(F("webServer: root"));
#endif
  server->sendHeader(F("Connection"), F("close"));
  time_t now = time(nullptr);
  String t = ctime(&now);
  t.trim();
  t = String(F("<span style='float: right'>")) + t + String(F("</span>"));
  char c[8];
  sprintf(c, "#%06X", Display::color565to888(Display::myColor));
  String payload = String(serverHead);
#ifdef DS18
  payload += PSTR("<p><meter value='") + String(display->Temp) + PSTR("' min='-50' max='150'></meter> Temperature\n");
#endif
  payload += String(serverOptions);
  payload.replace(F("%host%"), String(HOST) + t);
  payload.replace(F("%myColor%"), String(c));
  payload.replace(F("%brightness%"), String(config->brightness));
  payload.replace(F("%threshold%"), String(config->threshold));
  payload.replace(F("%milTime%"), config->milTime ? checked : "");
  payload.replace(F("%celsius%"), config->celsius ? checked : "");
  payload.replace(PSTR("'") + String(config->language) + PSTR("'"),
                  PSTR("'") + String(config->language) + PSTR("'") + PSTR(" selected"));
  payload.replace(F("%location%"), String(config->location));
  payload.replace(F("%timezone%"), String(config->timezone));
  payload.replace(F("%tzKey%"), String(config->tzKey));
  payload.replace(F("%owKey%"), String(config->owKey));
#ifdef SYSLOG
  payload.replace(F("%syslogSrv%"), String(config->syslogSrv));
  payload.replace(F("%syslogPort%"), String(config->syslogPort));
#endif
  payload.replace(F("%heap%"), String(fh));
#ifdef LIGHT
  payload.replace(F("%light%"), String(display->light));
#endif
  payload += String(serverUpdate);
  server->send(200, textHtml, payload);
}

void WebInterface::handleReset() {
  if (!handleAuth()) return reqAuth();
#ifdef SYSLOG
  syslog.log(F("webServer: reset"));
#endif
  OUT.println(F("webServer: reset"));
  server->send(200, textHtml, serverReboot);
  server->close();
  delay(1000);
  ESP.restart();
}

void WebInterface::handleDownload() {
  if (!handleAuth()) return reqAuth();
  String payload = config->getSPIFFS();
  server->sendHeader(F("Content-Disposition"), F("attachment; filename=config.json"));
  server->send(200, F("application/json"), payload);
}

void WebInterface::handleLogout() {
  server->send(401, textPlain, F("logged out"));
}

void WebInterface::start() {
  server->on(F("/"), HTTP_GET, [this]() { handleRoot(); });
  server->on(F("/stylesheet.css"), HTTP_GET, [this]() { handleStyle(); });
  server->on(F("/options"), [this]() { handleOptions(); });
  server->on(F("/reset"), HTTP_GET, [this]() { handleReset(); });
  server->on(F("/download"), HTTP_GET, [this]() { handleDownload(); });
  server->on(F("/logout"), HTTP_GET, [this]() { handleLogout(); });
  server->on(F("/favicon.ico"), HTTP_GET, [this]() {
    server->sendHeader(F("Location"), F("https://www.arduino.cc/favicon.ico"));
    server->send(301);
  });
  server->on(F("/update"), HTTP_POST, [this]() {
    if (!handleAuth()) return reqAuth();
#ifdef SYSLOG
    syslog.log(F("webServer: update"));
#endif
    server->send(200, textPlain, (Update.hasError()) ? F("FAIL") : F("OK"));
    server->close();
    delay(1000);
    ESP.restart();
  }, [this]() {
    if (!handleAuth()) return reqAuth();
    HTTPUpload& upload = server->upload();
    if (upload.status == UPLOAD_FILE_START) {
#if defined(ESP8266)
      WiFiUDP::stopAll();
#else
      MDNS.end();
#endif
      OUT.printf_P(PSTR("Update: %s\n"), upload.filename.c_str());
      uint32_t maxSketchSpace = (ESP.getFreeSketchSpace() - 0x1000) & 0xFFFFF000;
      if (!Update.begin(maxSketchSpace)) {
        Update.printError(Serial);
      }
    } else if (upload.status == UPLOAD_FILE_WRITE) {
      if (Update.write(upload.buf, upload.currentSize) != upload.currentSize) {
        Update.printError(Serial);
      }
    } else if (upload.status == UPLOAD_FILE_END) {
      if (Update.end(true)) {
        OUT.printf_P(PSTR("Update Success: %u\nRebooting...\n"), upload.totalSize);
      } else {
        Update.printError(Serial);
      }
    }
    yield();
  });
  server->onNotFound([this]() { handleNotFound(); });
  server->begin();
  MDNS.addService(F("_http"), F("_tcp"), 80);
}