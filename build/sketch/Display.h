#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/Display.h"
#ifndef DISPLAY_H
#define DISPLAY_H

#include <Arduino.h>
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include <Adafruit_GFX.h>
#include "Digit.h"

// Panel Configuration (matching esp32_ledmatrix_mqtt)
#define PANEL_RES_X 64
#define PANEL_RES_Y 32
#define PANEL_CHAIN 1
#define DEFAULT_BRIGHTNESS 80

// Pin Configuration (Waveshare P2.5 compatible)
#define R1_PIN 19
#define G1_PIN 13
#define B1_PIN 18
#define R2_PIN 5
#define G2_PIN 12
#define B2_PIN 17
#define A_PIN 16
#define B_PIN 14
#define C_PIN 4
#define D_PIN 27
#define E_PIN 25
#define LAT_PIN 26
#define OE_PIN 15
#define CLK_PIN 2

class Display {
private:
  MatrixPanel_I2S_DMA* dma_display;
  uint8_t dim;
  
public:
  Digit digit0;
  Digit digit1;
  Digit digit2;
  Digit digit3;
  Digit digit4;
  Digit digit5;
  
#ifdef LIGHT
  uint16_t light;
#endif

#ifdef DS18
  int Temp;
#endif

  Display();
  void begin();
  void displayDraw(uint8_t b);
  void showSplash();
  void showBootInfo(const char* hostname, IPAddress ip, const String& tz);
  
#ifdef LIGHT
  void getLight();
#endif

#ifdef DS18
  void updateTemperature();
#endif

  // Color definitions
  static const uint16_t myRED;
  static const uint16_t myGREEN;
  static const uint16_t myBLUE;
  static const uint16_t myLTBLUE;
  static const uint16_t myWHITE;
  static const uint16_t myYELLOW;
  static const uint16_t myORANGE;
  static const uint16_t myCYAN;
  static const uint16_t myMAGENTA;
  static const uint16_t myGRAY;
  static const uint16_t myBLACK;
  
  static const byte row1 = 6;
  static const byte row2 = 14;
  static const byte row3 = 22;
  static const byte row4 = 31;
  
  static uint16_t myColor;
  
  // Utility functions
  static uint16_t htmlColor565(const String htmlColor);
  static uint32_t color565to888(uint16_t c);
  
  MatrixPanel_I2S_DMA* getMatrix() { return dma_display; }
};

uint16_t htmlColor565(const String htmlColor);
uint32_t color565to888(uint16_t c);

extern Display displayObj;

#endif