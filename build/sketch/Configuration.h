#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/Configuration.h"
#ifndef CONFIGURATION_H
#define CONFIGURATION_H

#include <Arduino.h>

#define ADMIN_USER "admin"
#define WDELAY 900          // delay 15 min between weather updates
#define myOUT 1             // {0 = NullStream, 1 = Serial, 2 = Bluetooth}

#if myOUT == 0                    // NullStream output
class NullStream : public Stream {
public:
  NullStream(void) { return; }
  int available(void) { return 0; }
  void flush(void) { return; }
  int peek(void) { return -1; }
  int read(void) { return -1; }
  size_t write(uint8_t u_Data) { return u_Data, 0x01; }
};
extern NullStream NullStream;
extern Stream & OUT;
#elif myOUT == 2                  // Bluetooth output, only on ESP32
#include "BluetoothSerial.h"
extern BluetoothSerial SerialBT;
extern Stream & OUT;
#else                             // Serial output default
extern Stream & OUT;
#endif

class Configuration {
public:
  String tzKey;                     // API key from https://timezonedb.com/register
  String owKey;                     // API key from https://home.openweathermap.org/api_keys
  String softAPpass = "ConFigMe";   // password for SoftAP config and WebServer logon, minimum 8 characters
  uint8_t brightness = 255;         // 0-255 display brightness
  bool milTime = true;              // set false for 12hour clock
  String location;                  // zipcode or empty for geoIP location
  String timezone;                  // timezone from https://timezonedb.com/time-zones or empty for geoIP
  int threshold = 500;              // below this value display will dim, incrementally
  bool celsius = false;             // set true to display temp in celsius
  String language = "en";           // font does not support all languages
  String countryCode = "US";        // default US, automatically set based on public IP address

#ifdef SYSLOG
  String syslogSrv = "syslog";
  uint16_t syslogPort = 514;
#endif

  void readSPIFFS();
  void writeSPIFFS();
  String getSPIFFS();
};

extern Configuration config;

#endif