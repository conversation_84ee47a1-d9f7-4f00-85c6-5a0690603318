/home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/build/sketch/NetworkService.cpp.o: \
 /home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/build/sketch/NetworkService.cpp \
 /home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/build/sketch/NetworkService.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Arduino.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp_arduino_version.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/FreeRTOS.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/config/include/freertos/FreeRTOSConfig.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/config/xtensa/include/freertos/FreeRTOSConfig_arch.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa_config.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/hal.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/xtensa-versions.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core-isa.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core-matmap.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/tie.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/system.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa_context.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/corebits.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/xtruntime-frames.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/projdefs.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/portable.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/deprecated_definitions.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos/portmacro.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/xtruntime.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/specreg.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/xtruntime-core-state.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xt_instr_macros.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/xtruntime.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/spinlock.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_cpu.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/soc_caps.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/mpu_caps.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa_api.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xt_utils.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/extreg.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_bit_defs.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_attr.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_intr_alloc.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_err.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_compiler.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_intr_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_system/include/esp_private/crosscore_int.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_macros.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_assert.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_memory_utils.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/soc.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_assert.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/reg_base.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/newlib/platform_include/esp_newlib.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/heap/include/esp_heap_caps.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/heap/include/multi_heap.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_rom/include/esp_rom_sys.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/reset_reasons.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_system/include/esp_system.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_idf_version.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos/portbenchmark.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_timer/include/esp_timer.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_etm.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/mpu_wrappers.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/esp_additions/include/freertos/idf_additions.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/task.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/list.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/queue.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/task.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/semphr.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/queue.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/stream_buffer.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/message_buffer.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/stream_buffer.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/event_groups.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/timers.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp8266-compat.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/gpio_reg.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/stdlib_noniso.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/binary.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/extra_attr.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3/pins_arduino.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/io_pin_remap.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Arduino.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_sleep.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/touch_sensor_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/gpio_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/gpio_num.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-log.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_rom/esp32s3/include/esp32s3/rom/ets_sys.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/log/include/esp_log.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/log/include/esp_log_level.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/log/include/esp_log_color.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/log/include/esp_log_buffer.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/log/include/esp_log_timestamp.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-matrix.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/gpio_sig_map.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-uart.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/uart_pins.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/io_mux_reg.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/uart_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/clk_tree_defs.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-gpio.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_driver_gpio/include/driver/gpio.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_rom/include/esp_rom_gpio.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/gpio_pins.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_driver_gpio/include/driver/gpio_etm.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-touch.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-touch-ng.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-dac.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-adc.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-spi.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-i2c.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-ledc.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/ledc_types.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-rmt.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-sigmadelta.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-timer.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_driver_gptimer/include/driver/gptimer_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/timer_types.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-bt.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-psram.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-rgb-led.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-cpu.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/WCharacter.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/WString.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/pgmspace.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Stream.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Print.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Printable.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/IPAddress.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/lwip/src/include/lwip/ip_addr.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/lwip/src/include/lwip/opt.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/port/include/lwipopts.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/newlib/platform_include/sys/ioctl.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/newlib/platform_include/sys/poll.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_system/include/esp_task.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_random.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/port/include/sntp/sntp_get_set_time.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/port/include/sockets_ext.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/port/freertos/include/arch/sys_arch.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/lwip/src/include/lwip/debug.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/lwip/src/include/lwip/arch.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/port/esp32xx/include/arch/cc.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/newlib/platform_include/errno.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/lwip/src/include/lwip/def.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/lwip/src/include/lwip/ip4_addr.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/lwip/src/include/lwip/ip6_addr.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/lwip/src/include/lwip/def.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/lwip/lwip/src/include/lwip/ip6_zone.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_netif/include/esp_netif_ip_addr.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Client.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Server.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Udp.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/HardwareSerial.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/HWCDC.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_event/include/esp_event.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_event/include/esp_event_base.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_driver_usb_serial_jtag/include/driver/usb_serial_jtag.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/USBCDC.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Esp.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_partition/include/esp_partition.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/hal/cpu_hal.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/hal/cpu_ll.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/freertos_stats.h \
 /home/<USER>/Documents/Arduino/libraries/WiFiManager/WiFiManager.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFi.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Print.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiType.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_wifi/include/esp_wifi_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_wifi/include/esp_wifi_types_generic.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_interface.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_wifi/include/local/esp_wifi_types_native.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_wifi/include/esp_wifi_types_generic.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiSTA.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiGeneric.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_wifi/include/esp_smartconfig.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_netif/include/esp_netif_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_netif/include/esp_netif_ip_addr.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_eth/include/esp_eth_driver.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_eth/include/esp_eth_com.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/eth_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_eth/include/esp_eth_spec.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_eth/include/esp_eth_mac_spi.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_eth/include/esp_eth_mac.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_driver_spi/include/driver/spi_master.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/spi_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_driver_spi/include/driver/spi_common.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_system/include/esp_ipc.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_eth/include/esp_eth_phy.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/espressif__network_provisioning/include/network_provisioning/manager.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/protocomm/include/common/protocomm.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/protocomm/include/security/protocomm_security.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/espressif__network_provisioning/include/network_provisioning/network_config.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/Network.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkInterface.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Printable.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkEvents.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkManager.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/WString.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkClient.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Client.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkServer.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Server.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkUdp.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Udp.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/cbuf.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_ringbuf/include/freertos/ringbuf.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiAP.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiScan.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiClient.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiServer.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiUdp.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkUdp.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_wifi/include/esp_wifi.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_wifi/include/esp_wifi_crypto_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_wifi/include/esp_wifi_default.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_netif/include/esp_netif.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_netif/include/esp_netif_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_netif/include/esp_netif_defaults.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Update/src/Update.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/MD5Builder.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_rom/include/esp_rom_md5.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/HashBuilder.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/HEXBuilder.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/WebServer.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src/FS.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/HTTP_Method.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/http_parser/http_parser.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/Uri.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/middleware/Middleware.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/detail/RequestHandler.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src/DNSServer.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src/AsyncUDP.h \
 /home/<USER>/Documents/Arduino/libraries/WiFiManager/wm_strings_en.h \
 /home/<USER>/Documents/Arduino/libraries/WiFiManager/wm_consts_en.h \
 /home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/build/sketch/Configuration.h \
 /home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/build/sketch/Display.h \
 /home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/ESP32-HUB75-MatrixPanel-I2S-DMA.h \
 /home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/platform_detect.hpp \
 /home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32s3/gdma_lcd_parallel16.hpp \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_lcd/include/esp_lcd_panel_io.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_lcd/include/esp_lcd_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/lcd_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/color_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/mipi_dsi_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_lcd/include/esp_lcd_io_i80.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_lcd/include/esp_lcd_io_i2c.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_driver_i2c/include/driver/i2c_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/i2c_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/hal_utils.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_lcd/include/esp_lcd_io_spi.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/esp32s3/include/hal/gpio_ll.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/include/soc/gpio_periph.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/gpio_struct.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/rtc_cntl_reg.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/rtc_io_reg.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/usb_serial_jtag_reg.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/lcd_hal.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/dma/include/esp_private/gdma.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/gdma_channel.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/gdma_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/dma_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/gpio_hal.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/esp32s3/include/hal/lcd_ll.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/platform_port/include/hal/misc.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/lcd_cam_reg.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/lcd_cam_struct.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/platform_port/include/hal/assert.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/system_struct.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/heap/include/esp_heap_caps_init.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_private/periph_ctrl.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/periph_defs.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/interrupts.h \
 /home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32s3/esp32s3-default-pins.hpp \
 /home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/Adafruit_GFX.h \
 /home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/gfxfont.h \
 /home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_I2CDevice.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src/Wire.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-log.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/HardwareI2C.h \
 /home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_SPIDevice.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src/SPI.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-spi.h \
 /home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/build/sketch/Digit.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src/NetworkClientSecure.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src/ssl_client.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/platform.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/private_access.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/build_info.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/mbedtls/esp_config.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/mbedtls_config.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/esp_mem.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/config_psa.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_legacy.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_adjust_config_synonyms.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_adjust_config_dependencies.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/config_adjust_psa_superset_legacy.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/config_adjust_psa_from_legacy.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_adjust_config_key_pair_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_adjust_auto_enabled.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/config_adjust_legacy_crypto.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/config_adjust_x509.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/config_adjust_ssl.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/check_config.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/platform_time.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/net_sockets.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/ssl.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/platform_util.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/mbedtls/bignum.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/bignum.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/mbedtls/ecp.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/ecp.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/ssl_ciphersuites.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/pk.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/md.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/rsa.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/ecdsa.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_platform.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/build_info.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_values.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_sizes.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_struct.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_driver_contexts_primitives.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_driver_common.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_sizes.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_builtin_primitives.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/md5.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/md5_alt.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/md/esp_md.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/ripemd160.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/sha1.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/sha1_alt.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/sha_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_rom/esp32s3/include/esp32s3/rom/sha.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/sha256.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/sha256_alt.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/sha512.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/sha512_alt.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/sha3.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/cipher.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_driver_contexts_composites.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_builtin_composites.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/cmac.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/mbedtls/gcm.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/gcm.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/gcm_alt.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/aes/esp_aes_gcm.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/aes/esp_aes.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/aes_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/ccm.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/chachapoly.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/poly1305.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/chacha20.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/ecjpake.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_driver_contexts_key_derivation.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_builtin_key_derivation.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_extra.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/psa/crypto_compat.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/x509_crt.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/x509.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/asn1.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/x509_crl.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/ecdh.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/debug.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/entropy.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/md.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/ctr_drbg.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/aes.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/port/include/aes_alt.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/entropy.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/mbedtls/mbedtls/include/mbedtls/error.h \
 /home/<USER>/Documents/Arduino/libraries/ArduinoJson/src/ArduinoJson.h \
 /home/<USER>/Documents/Arduino/libraries/ArduinoJson/src/ArduinoJson.hpp \
 /home/<USER>/Documents/Arduino/libraries/ArduinoJson/src/ArduinoJson/Configuration.hpp
