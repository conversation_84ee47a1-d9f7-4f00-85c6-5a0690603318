#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/NetworkService.h"
#ifndef NETWORKSERVICE_H
#define NETWORKSERVICE_H

#include <Arduino.h>
#include <WiFiManager.h>

#ifdef SYSLOG
#include <Syslog.h>
#include <WiFiUdp.h>
extern WiFiUDP udpClient;
extern Syslog syslog;
#endif

class NetworkService {
private:
  time_t wDelay;
  String degreeDir(int degrees);
  byte utf8ascii(byte ascii);
  String utf8ascii(String s);
  void utf8ascii(char* s);
  
public:
  void startWiFi();
  String getIPlocation();
  void getWeather();
  void setWeatherDelay(time_t delay) { wDelay = delay; }
  time_t getWeatherDelay() { return wDelay; }
};

extern NetworkService network;

#endif