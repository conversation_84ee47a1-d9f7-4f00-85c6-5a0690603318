#include <Arduino.h>
#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/myClockOrganized.ino"
/*   myClockOrganized -- ESP8266/ESP32 WiFi NTP Clock for pixel displays
     Copyright (c) 2019 <PERSON> <<EMAIL>>
     distributed under the terms of the MIT License
     Reorganized for better code structure
*/

#include <ArduinoJson.h>
#include <WiFiManager.h>
#include <ArduinoOTA.h>
#include <FS.h>
#include <time.h>

#include "Configuration.h"
#include "Display.h"
#include "ClockTime.h"
#include "NetworkService.h"
#include "WebInterface.h"

#if defined(ESP8266)
#include <ESP8266mDNS.h>
ESP8266WebServer server(80);
#else
#include <ESPmDNS.h>
#include <SPIFFS.h>
WebServer server(80);
#endif

#if myOUT == 2
#include "BluetoothSerial.h"
BluetoothSerial SerialBT;
#endif

#define APPNAME "myClockOrganized"
#define VERSION "1.0.0"

Configuration config;
Display displayObj;
ClockTime clockTime;
NetworkService network;
WebInterface webInterface(&server, &config, &displayObj, &clockTime, &network);

time_t pNow, wDelay;
uint8_t pHH, pMM, pSS;
char HOST[20];

#line 46 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/myClockOrganized.ino"
void setup();
#line 93 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/myClockOrganized.ino"
void loop();
#line 46 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/X2/myClockOrganized/myClockOrganized.ino"
void setup() {
#if myOUT == 0
  Serial.end();
#else
  Serial.begin(115200);
  while (!Serial) delay(10);
  Serial.println();
#endif

#if myOUT == 2
  if (!SerialBT.begin(APPNAME)) {
    Serial.println(F("Bluetooth failed"));
    delay(5000);
    ESP.restart();
  }
  delay(5000);
#endif

  config.readSPIFFS();
  
  displayObj.begin();
  displayObj.showSplash();

#ifdef DS18
  sensors.begin();
#endif

  network.startWiFi();
  
  if (config.location == "") {
    config.location = network.getIPlocation();
  } else {
    while (config.timezone == "") {
      network.getIPlocation();
    }
  }

  displayObj.showBootInfo(HOST, WiFi.localIP(), config.timezone);
  
  clockTime.setNTP(config.timezone);
  delay(1000);
  
  webInterface.start();
  displayObj.displayDraw(config.brightness);
  network.getWeather();
}

void loop() {
#if defined(ESP8266)
  MDNS.update();
#endif
  ArduinoOTA.handle();
  server.handleClient();
  
  struct tm * timeinfo;
  time_t now = time(nullptr);
  timeinfo = localtime(&now);
  
  if (now != pNow) {
    if (now > clockTime.getTwoAM()) {
      clockTime.setNTP(config.timezone);
    }
    
    int ss = timeinfo->tm_sec;
    int mm = timeinfo->tm_min;
    int hh = timeinfo->tm_hour;
    if ((!config.milTime) && (hh > 12)) hh -= 12;
    
    if (ss != pSS) {
      int s0 = ss % 10;
      int s1 = ss / 10;
      if (s0 != displayObj.digit0.Value()) displayObj.digit0.Morph(s0);
      if (s1 != displayObj.digit1.Value()) displayObj.digit1.Morph(s1);
      pSS = ss;
#ifdef LIGHT
      displayObj.getLight();
#endif
    }
    
    if (mm != pMM) {
      int m0 = mm % 10;
      int m1 = mm / 10;
      if (m0 != displayObj.digit2.Value()) displayObj.digit2.Morph(m0);
      if (m1 != displayObj.digit3.Value()) displayObj.digit3.Morph(m1);
      pMM = mm;
      OUT.printf_P(PSTR("%02d:%02d %d "), hh, mm, ESP.getFreeHeap());
#ifdef LIGHT
      OUT.print(displayObj.light);
#endif
      OUT.print("\r");
    }
    
    if (hh != pHH) {
      int h0 = hh % 10;
      int h1 = hh / 10;
      if (h0 != displayObj.digit4.Value()) displayObj.digit4.Morph(h0);
      if (h1 != displayObj.digit5.Value()) displayObj.digit5.Morph(h1);
      pHH = hh;
    }
    
#ifdef DS18
    displayObj.updateTemperature();
#endif
    
    pNow = now;
    if (now > wDelay) network.getWeather();
  }
}
